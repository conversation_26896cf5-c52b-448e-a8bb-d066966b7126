import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import CognitoRegister from '../CognitoRegister.vue'

// Mock the stores and composables
vi.mock('../../../stores/cognitoAuth', () => ({
  useCognitoAuthStore: () => ({
    // Mock store methods if needed
  })
}))

vi.mock('../../../composables/useApi', () => ({
  default: () => ({
    apiExecute: vi.fn()
  })
}))

// Create a mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/login', component: { template: '<div>Login</div>' } }
  ]
})

describe('CognitoRegister', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(CognitoRegister, {
      global: {
        plugins: [router]
      }
    })
  })

  it('renders the registration form', () => {
    expect(wrapper.find('h1').text()).toBe('会員登録')
    expect(wrapper.find('p').text()).toBe('オークション会員アカウントを作成')
  })

  it('displays all required form fields', () => {
    // Check for member name input
    const memberNameInput = wrapper.find('input[placeholder="会員名"]')
    expect(memberNameInput.exists()).toBe(true)

    // Check for email input
    const emailInput = wrapper.find('input[placeholder="メールアドレス"]')
    expect(emailInput.exists()).toBe(true)

    // Check for password inputs
    const passwordInputs = wrapper.findAll('input[type="password"]')
    expect(passwordInputs).toHaveLength(2)

    // Check for language select
    const languageSelect = wrapper.find('select')
    expect(languageSelect.exists()).toBe(true)
  })

  it('shows validation errors for empty fields', async () => {
    // Try to submit empty form
    const submitButton = wrapper.find('#registerBtn')
    await submitButton.trigger('click')

    // Check that form validation prevents submission
    expect(wrapper.vm.isFormValid).toBe(false)
  })

  it('validates email format', async () => {
    const emailInput = wrapper.find('input[placeholder="メールアドレス"]')
    
    // Test invalid email
    await emailInput.setValue('invalid-email')
    await emailInput.trigger('blur')
    
    expect(wrapper.vm.errors.email).toBe('有効なメールアドレスを入力してください')
  })

  it('validates password strength', async () => {
    const passwordInput = wrapper.find('input[placeholder="パスワード"]')
    
    // Test weak password
    await passwordInput.setValue('weak')
    await passwordInput.trigger('blur')
    
    expect(wrapper.vm.errors.password).toBe('パスワードは8文字以上で入力してください')
  })

  it('validates password confirmation match', async () => {
    const passwordInput = wrapper.find('input[placeholder="パスワード"]')
    const confirmInput = wrapper.find('input[placeholder="パスワード（確認用）"]')
    
    await passwordInput.setValue('TestPass123!')
    await confirmInput.setValue('DifferentPass123!')
    await confirmInput.trigger('blur')
    
    expect(wrapper.vm.errors.passwordConfirm).toBe('パスワードが一致しません')
  })

  it('enables submit button when form is valid', async () => {
    // Fill in valid form data
    await wrapper.find('input[placeholder="会員名"]').setValue('テストユーザー')
    await wrapper.find('input[placeholder="メールアドレス"]').setValue('<EMAIL>')
    await wrapper.find('input[placeholder="パスワード"]').setValue('TestPass123!')
    await wrapper.find('input[placeholder="パスワード（確認用）"]').setValue('TestPass123!')
    await wrapper.find('select').setValue('ja')

    // Trigger validation
    await wrapper.vm.$nextTick()

    expect(wrapper.vm.isFormValid).toBe(true)
    expect(wrapper.find('#registerBtn').attributes('disabled')).toBeUndefined()
  })

  it('shows success message after successful registration', async () => {
    // Set registration success state
    await wrapper.setData({ registrationSuccess: true })
    await wrapper.setData({ 
      createdUser: {
        memberName: 'テストユーザー',
        email: '<EMAIL>',
        language: 'ja',
        tenantId: '1'
      }
    })

    expect(wrapper.find('h3').text()).toBe('会員登録が完了しました')
    expect(wrapper.text()).toContain('テストユーザー')
    expect(wrapper.text()).toContain('<EMAIL>')
  })

  it('has navigation buttons in success state', async () => {
    await wrapper.setData({ registrationSuccess: true })

    const buttons = wrapper.findAll('button')
    expect(buttons).toHaveLength(2)
    expect(buttons[0].text()).toBe('戻る')
    expect(buttons[1].text()).toBe('ログイン画面へ')
  })

  it('resets form when clicking back button', async () => {
    // Set success state first
    await wrapper.setData({ registrationSuccess: true })
    
    // Click back button
    const backButton = wrapper.find('button:first-child')
    await backButton.trigger('click')

    expect(wrapper.vm.registrationSuccess).toBe(false)
    expect(wrapper.vm.formData.memberName).toBe('')
    expect(wrapper.vm.formData.email).toBe('')
  })
})
