# Cognito Registration Integration Test Guide

## Manual Testing Steps

### 1. **Access the Registration Page**
- Navigate to: `http://localhost:5174/cognito-register`
- Verify the page loads without errors
- Check that all form fields are visible

### 2. **Test Form Validation**

#### Empty Form Submission:
- Click "アカウント作成" button without filling any fields
- Verify button is disabled when form is invalid

#### Email Validation:
- Enter invalid email: `invalid-email`
- Tab out of field
- Verify error message: "有効なメールアドレスを入力してください"

#### Password Validation:
- Enter weak password: `weak`
- Tab out of field
- Verify error message: "パスワードは8文字以上で入力してください"

#### Password Confirmation:
- Enter password: `TestPass123!`
- Enter different confirmation: `DifferentPass123!`
- Tab out of confirmation field
- Verify error message: "パスワードが一致しません"

### 3. **Test Successful Registration**

#### Valid Form Data:
```
Member Name: テストユーザー
Email: test-user-$(timestamp)@example.com
Password: TestPass123!
Password Confirmation: TestPass123!
Language: 日本語
```

#### Expected Flow:
1. Fill form with valid data
2. Click "アカウント作成" button
3. See loading spinner
4. On success: See success message with user details
5. Verify "戻る" and "ログイン画面へ" buttons appear

### 4. **Test Error Handling**

#### Duplicate Email:
- Use same email twice
- Verify error message: "このメールアドレスは既に登録されています"

#### Network Error:
- Disconnect internet or stop backend
- Verify error message: "ネットワークエラーが発生しました。もう一度お試しください。"

### 5. **Test Navigation**

#### Success State Navigation:
- After successful registration, click "ログイン画面へ"
- Verify navigation to `/login` page

#### Back Button:
- After successful registration, click "戻る"
- Verify form resets and returns to registration form

### 6. **Test Backend Integration**

#### API Endpoint Verification:
- Open browser developer tools
- Monitor Network tab during registration
- Verify POST request to: `${VITE_API_ENDPOINT}cognito-register-member`

#### Request Payload:
```json
{
  "registerData": {
    "memberName": "テストユーザー",
    "email": "<EMAIL>",
    "password": "TestPass123!",
    "passwordConfirm": "TestPass123!",
    "language": "ja"
  },
  "languageCode": "ja"
}
```

#### Expected Response:
```json
{
  "message": "Registration successful",
  "tenantId": "1",
  "userId": "cognito-user-id"
}
```

### 7. **Test Responsive Design**

#### Mobile View:
- Resize browser to mobile width (375px)
- Verify form layout adapts properly
- Check that all elements remain accessible

#### Desktop View:
- Test on standard desktop resolution (1920x1080)
- Verify centered layout and proper spacing

## Automated Testing

### Run Unit Tests:
```bash
cd auction-side
npm run test:unit src/components/register/__tests__/CognitoRegister.test.js
```

### Expected Results:
- ✅ 6+ tests passing
- ✅ Core functionality validated
- ✅ Form validation working
- ✅ Component rendering correctly

## Environment Variables Check

Verify these environment variables are set:
```bash
# In auction-side/.env.demo2 and .env.development
VITE_API_ENDPOINT=https://your-api-gateway-url/
VITE_AUCTION_USER_POOL_ID=ap-northeast-1_cDC4pP8me
VITE_AUCTION_CLIENT_ID=3t20er2kjs8rmdeq4vekm5fdoq
```

## Troubleshooting

### Common Issues:

1. **Component not loading**: Check router configuration
2. **API errors**: Verify environment variables and Lambda deployment
3. **Validation not working**: Check form field names and validation functions
4. **Styling issues**: Verify Bootstrap classes and custom CSS

### Debug Steps:

1. Check browser console for JavaScript errors
2. Verify network requests in developer tools
3. Check Vue DevTools for component state
4. Validate environment variables are loaded correctly
