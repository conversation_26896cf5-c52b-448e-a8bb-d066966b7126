<script setup lang="ts">
  import {
    useRoute,
    useRouter,
    type RouteLocationNormalizedLoaded,
    type Router,
  } from 'vue-router'
  import {PATH_NAME} from '../../defined/const.js'

  // // Router and route
  const route: RouteLocationNormalizedLoaded = useRoute()
  const router: Router = useRouter()

  const handleLogin = () => {
    console.log('Logging in...')
    router.replace(route.redirectedFrom?.path ?? PATH_NAME.TOP)
  }

  const navigateToReminder = () => {
    router.push(PATH_NAME.REMINDER)
  }
</script>

<template>
  <h2 class="page-ttl">
    <p class="ttl">ログイン</p>
    <p class="sub">login</p>
  </h2>
  <div class="container">
    <section id="login-form">
      <form>
        <div class="id-pass-err">
          <span class="err-txt">
            ログインIDまたはパスワードが正しくありません。
          </span>
        </div>
        <table class="tbl-login">
          <tbody>
            <tr>
              <th>ログインID<em class="req">※必須</em></th>
              <td>
                <input
                  type="text"
                  class="ime-dis err"
                  placeholder="8～14文字の半角英数字"
                  required
                />
                <p class="err-txt">未入力です</p>
              </td>
            </tr>
            <tr>
              <th>パスワード<em class="req">※必須</em></th>
              <td>
                <input
                  type="password"
                  class="ime-dis err"
                  placeholder="8～14文字の半角英数字"
                  required
                />
                <p class="err-txt">未入力です</p>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="check-idpass">
          <label>
            <input type="checkbox" class="checkbox-input" />
            <span class="checkbox-parts">ID・パスワードを保存</span>
          </label>
        </div>
        <div class="forget-pass">
          <a @click="navigateToReminder">パスワードを忘れた方はコチラ</a>
        </div>

        <div class="rule">
          <p class="tit-rule">入札会参加要項</p>
          <embed
            src="/pdf/sample.pdf"
            type="application/pdf"
            width="100%"
            height="150"
          />
          <div class="rule-check">
            <label for="rule-chk">
              <input
                type="checkbox"
                id="rule-chk"
                class="checkbox-input"
                required
              />
              <span class="checkbox-parts">参加規約に同意する</span>
            </label>
          </div>
        </div>
        <div class="btn-form">
          <input
            type="button"
            id="sbm-login"
            @click="handleLogin"
            value="ログイン"
          />
        </div>
      </form>
      <div class="request">
        <a class="register-btt" @click="() => router.push(PATH_NAME.REGISTER)"
          >新規会員登録（詳細）</a
        >
        <span class="mx-2">|</span>
        <a
          class="register-btt"
          @click="() => router.push(PATH_NAME.COGNITO_REGISTER)"
          >簡単登録（Cognito）</a
        >
        <p>※商品の価格を見るには会員登録が必要です。</p>
      </div>
    </section>
  </div>
</template>

<style lang="css">
  .register-btt {
    cursor: pointer;
  }
</style>
